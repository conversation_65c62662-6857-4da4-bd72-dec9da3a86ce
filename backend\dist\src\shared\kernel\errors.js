"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppError = exports.ErrorCode = void 0;
exports.formatError = formatError;
var ErrorCode;
(function (ErrorCode) {
    ErrorCode["UNAUTHENTICATED"] = "UNAUTHENTICATED";
    ErrorCode["UNAUTHORIZED"] = "UNAUTHORIZED";
    ErrorCode["INVALID_CREDENTIALS"] = "INVALID_CREDENTIALS";
    ErrorCode["TOKEN_EXPIRED"] = "TOKEN_EXPIRED";
    ErrorCode["VALIDATION_ERROR"] = "VALIDATION_ERROR";
    ErrorCode["INVALID_INPUT"] = "INVALID_INPUT";
    ErrorCode["REQUIRED_FIELD_MISSING"] = "REQUIRED_FIELD_MISSING";
    ErrorCode["RESOURCE_NOT_FOUND"] = "RESOURCE_NOT_FOUND";
    ErrorCode["RESOURCE_ALREADY_EXISTS"] = "RESOURCE_ALREADY_EXISTS";
    ErrorCode["OPERATION_NOT_ALLOWED"] = "OPERATION_NOT_ALLOWED";
    ErrorCode["BUSINESS_RULE_VIOLATION"] = "BUSINESS_RULE_VIOLATION";
    ErrorCode["INTERNAL_SERVER_ERROR"] = "INTERNAL_SERVER_ERROR";
    ErrorCode["DATABASE_ERROR"] = "DATABASE_ERROR";
    ErrorCode["EXTERNAL_SERVICE_ERROR"] = "EXTERNAL_SERVICE_ERROR";
    ErrorCode["RATE_LIMIT_EXCEEDED"] = "RATE_LIMIT_EXCEEDED";
})(ErrorCode || (exports.ErrorCode = ErrorCode = {}));
class AppError extends Error {
    code;
    statusCode;
    details;
    constructor(code, message, statusCode = 500, details) {
        super(message);
        this.code = code;
        this.statusCode = statusCode;
        this.details = details;
        this.name = 'AppError';
        Error.captureStackTrace(this, AppError);
    }
    static unauthenticated(message = 'Authentication required') {
        return new AppError(ErrorCode.UNAUTHENTICATED, message, 401);
    }
    static unauthorized(message = 'Insufficient permissions') {
        return new AppError(ErrorCode.UNAUTHORIZED, message, 403);
    }
    static invalidCredentials(message = 'Invalid credentials') {
        return new AppError(ErrorCode.INVALID_CREDENTIALS, message, 401);
    }
    static notFound(resource, id) {
        const message = id ? `${resource} with id ${id} not found` : `${resource} not found`;
        return new AppError(ErrorCode.RESOURCE_NOT_FOUND, message, 404);
    }
    static alreadyExists(resource, field) {
        const message = field ? `${resource} with this ${field} already exists` : `${resource} already exists`;
        return new AppError(ErrorCode.RESOURCE_ALREADY_EXISTS, message, 409);
    }
    static validation(message, details) {
        return new AppError(ErrorCode.VALIDATION_ERROR, message, 400, details);
    }
    static businessRule(message) {
        return new AppError(ErrorCode.BUSINESS_RULE_VIOLATION, message, 400);
    }
    static internal(message = 'Internal server error') {
        return new AppError(ErrorCode.INTERNAL_SERVER_ERROR, message, 500);
    }
}
exports.AppError = AppError;
function formatError(error, path) {
    if (error instanceof AppError) {
        return {
            code: error.code,
            message: error.message,
            statusCode: error.statusCode,
            details: error.details,
            timestamp: new Date().toISOString(),
            path,
        };
    }
    if (error.name === 'PrismaClientKnownRequestError') {
        const prismaError = error;
        switch (prismaError.code) {
            case 'P2002':
                return formatError(AppError.alreadyExists('Resource', 'unique field'), path);
            case 'P2025':
                return formatError(AppError.notFound('Resource'), path);
            default:
                return formatError(AppError.internal('Database operation failed'), path);
        }
    }
    if (error.name === 'ZodError') {
        return formatError(AppError.validation('Validation failed', { errors: error.errors }), path);
    }
    const errorMessage = typeof error.message === 'string' && error.message.length > 0
        ? error.message
        : 'An unexpected error occurred';
    const internalError = AppError.internal(errorMessage);
    return {
        code: internalError.code,
        message: internalError.message,
        statusCode: internalError.statusCode,
        details: internalError.details,
        timestamp: new Date().toISOString(),
        path,
    };
}
//# sourceMappingURL=errors.js.map