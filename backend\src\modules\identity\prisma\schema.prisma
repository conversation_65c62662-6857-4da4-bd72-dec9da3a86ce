model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String?
  lastName  String?
  role      UserRole @default(Student)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum UserRole {
  SuperAdmin
  FounderInvestor
  Principal
  SchoolAdmin
  Teacher
  Cleaner
  Driver
  StaffOther
  Student
  ParentGuardian
}
