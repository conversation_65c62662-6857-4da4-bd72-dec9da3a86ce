{"modules": {"Identity Module": {"description": "Central authentication and authorization hub for all system users", "features": ["Multi-factor authentication with JWT tokens", "Role-school association management", "Permission aggregation across roles/schools", "User profile management", "Password recovery and account locking", "Session management across devices"], "userCapabilities": {"SuperAdmin": ["Create and manage all schools", "Create founder/investor accounts", "Assign schools to founders/investors", "Manage all user accounts", "Configure system-wide authentication policies"], "FounderInvestor": ["View linked schools", "Create principals/school admins for linked schools", "Manage their own profile"], "Principal": ["Manage their own profile", "View their permissions", "Manage sessions for their school"], "SchoolAdmin": ["Manage their own profile", "View their permissions", "Manage sessions for their school"], "Staff": {"Teacher": ["Manage their own profile", "View their permissions", "Manage their sessions"], "Cleaner": ["Manage their own profile", "View their schedule"], "Driver": ["Manage their own profile", "View their schedule"], "Other": ["Manage their own profile", "View their schedule"]}, "Student": ["Manage their own profile", "View their permissions", "Reset their password"], "ParentGuardian": ["Manage their own profile", "Link to their children", "Reset their password"]}, "integrations": {"All Modules": "Provides authentication and authorization services", "School Module": "Validates school-specific permissions", "Staff Module": "Manages staff credentials and roles", "Students Module": "Handles student accounts and parent links"}, "endGoals": ["Unified login experience for all roles", "Seamless permission aggregation across schools", "Secure credential management with <PERSON><PERSON>'s keytar", "Real-time permission updates"], "multiRoleSupport": {"description": "Handles users with multiple roles across schools", "implementation": {"dataAggregation": "Aggregates permissions and data from all roles/schools", "permissionCombination": "Uses OR logic - if any role grants permission, access is allowed", "uiPresentation": "Groups data by role then school with clear visual indicators"}}, "uiGuidelines": {"multiRoleUsers": {"contextIndicators": "Show current role and school context in the UI", "navigation": "Role-based tabs with state preservation when switching", "dataGrouping": "Group information by role then school with expandable sections"}}, "realTimeUpdates": {"multiRoleHandling": {"subscriptionLogic": "Users receive updates for all roles/schools they're associated with", "notificationRouting": "Notifications tagged with role/school context", "uiUpdates": "Real-time updates appear in relevant role/school sections"}}}, "School Module": {"description": "Manages institutional structure and academic calendar", "features": ["School creation and configuration", "Academic year/term management", "Holiday calendar management", "Facility and resource management", "School-level settings and policies"], "userCapabilities": {"SuperAdmin": ["Create and manage all schools", "Configure global school settings", "Manage academic calendars for all schools"], "FounderInvestor": ["View linked schools", "View academic calendar for linked schools", "View facility usage for linked schools"], "Principal": ["Manage their school's profile", "Configure academic calendar", "Manage facilities", "Set school policies"], "SchoolAdmin": ["View school profile", "View academic calendar", "Manage facility bookings", "Update school policies"], "Staff": {"Teacher": ["View school calendar", "Book facilities for classes", "View school policies"], "Cleaner": ["View cleaning schedule", "Report facility issues"], "Driver": ["View transportation schedule", "Report vehicle issues"], "Other": ["View relevant school information", "Access facilities as needed"]}, "Student": ["View school calendar", "View facility information", "Access school policies"], "ParentGuardian": ["View school calendar", "View facility information", "Access school policies"]}, "integrations": {"Identity Module": "Validates school creation permissions", "Academy Module": "Provides curriculum context", "Timetable Module": "Supplies academic calendar data", "Finance Module": "Manages school-level financial settings", "Analytics Module": "Supplies institutional metrics"}, "endGoals": ["Complete institutional profile management", "Standardized academic calendar across all modules", "Centralized school policies enforcement", "Facility utilization tracking"], "multiRoleSupport": {"description": "Manages school data for users with roles in multiple schools", "implementation": {"dataAggregation": "Shows school data from all schools user has roles in", "permissionCombination": "School-specific permissions apply only to that school", "uiPresentation": "School selector dropdown with clear school indicators"}}, "uiGuidelines": {"multiRoleUsers": {"contextIndicators": "Clear labeling of which school data belongs to", "navigation": "School selector dropdown when user has roles in multiple schools", "dataGrouping": "Group information by school with color-coding"}}, "realTimeUpdates": {"multiRoleHandling": {"subscriptionLogic": "Users receive calendar and facility updates for all schools", "notificationRouting": "Notifications include school context", "uiUpdates": "Calendar shows events from all schools with school indicators"}}}, "Staff Module": {"description": "Comprehensive staff management system", "features": ["Staff directory and profiles", "Employment contract management", "Attendance tracking", "Performance evaluation system", "Department and role management"], "userCapabilities": {"SuperAdmin": ["Manage all staff across all schools", "Configure global staff policies", "View system-wide staff analytics"], "FounderInvestor": ["View staff directory for linked schools", "View performance metrics for linked schools"], "Principal": ["Manage all staff at their school", "Approve staff activation and salary", "Conduct performance evaluations", "Manage departments"], "SchoolAdmin": ["View staff directory", "Manage staff attendance", "Process leave requests", "Update staff profiles"], "Staff": {"Teacher": ["View their profile", "Record their attendance", "View their schedule", "View performance evaluations"], "Cleaner": ["View their profile", "Record their attendance", "View their cleaning schedule"], "Driver": ["View their profile", "Record their attendance", "View their transportation schedule"], "Other": ["View their profile", "Record their attendance", "View their schedule"]}, "Student": ["View teacher directory", "View teacher profiles"], "ParentGuardian": ["View teacher directory", "View teacher profiles", "Contact teachers"]}, "integrations": {"Identity Module": "Manages staff credentials", "School Module": "Aligns with school policies", "Attendance Module": "Tracks staff attendance", "Finance Module": "Handles payroll integration", "Timetable Module": "Manages teaching assignments"}, "endGoals": ["Complete staff lifecycle management", "Automated attendance tracking", "Performance evaluation system", "Departmental resource allocation"], "multiRoleSupport": {"description": "Handles staff who may have roles in multiple schools", "implementation": {"dataAggregation": "Shows staff roles and assignments across all schools", "permissionCombination": "Role-specific permissions apply per school", "uiPresentation": "Staff profile shows all roles and schools with clear indicators"}}, "uiGuidelines": {"multiRoleUsers": {"contextIndicators": "Show current role and school context in staff profile", "navigation": "Role/school selector for staff with multiple assignments", "dataGrouping": "Group assignments by school then role"}}, "realTimeUpdates": {"multiRoleHandling": {"subscriptionLogic": "Staff receive updates for all roles/schools they're assigned to", "notificationRouting": "Notifications include role/school context", "uiUpdates": "Schedule shows assignments from all schools"}}}, "Students Module": {"description": "Student information and academic progress management", "features": ["Student enrollment and registration", "Academic profile management", "Attendance tracking", "Grade and performance tracking", "Health records management", "Parent portal integration"], "userCapabilities": {"SuperAdmin": ["View all students across all schools", "Configure global student policies", "Access system-wide student analytics"], "FounderInvestor": ["View student statistics for linked schools", "View performance metrics for linked schools"], "Principal": ["View all students at their school", "Manage enrollment", "View student performance", "Access health records"], "SchoolAdmin": ["Manage student enrollment", "Update student profiles", "Record attendance", "Generate student reports"], "Staff": {"Teacher": ["View their class students", "Record attendance", "Record grades", "View student profiles"], "Cleaner": ["View student information relevant to their duties"], "Driver": ["View student transportation assignments"], "Other": ["View student information relevant to their role"]}, "Student": ["View their own profile", "View their grades", "View their attendance", "View their schedule"], "ParentGuardian": ["View their children's profiles", "View their children's grades", "View their children's attendance", "View their children's schedule", "Communicate with teachers"]}, "integrations": {"Identity Module": "Links student to parent accounts", "Academy Module": "Tracks academic progress", "Attendance Module": "Records student attendance", "Finance Module": "Manages fee accounts", "Examination Module": "Records assessment results"}, "endGoals": ["Complete student lifecycle management", "Real-time academic progress tracking", "Integrated parent communication", "Health and safety compliance"], "multiRoleSupport": {"description": "<PERSON>les parents who may have children in multiple schools", "implementation": {"dataAggregation": "Shows all children across all schools in unified view", "permissionCombination": "Parent permissions apply to all their children", "uiPresentation": "Children grouped by school with clear indicators"}}, "uiGuidelines": {"multiRoleUsers": {"contextIndicators": "Clear labeling of which school each child attends", "navigation": "School-based tabs or sections for children", "dataGrouping": "Group children by school with expandable sections"}}, "realTimeUpdates": {"multiRoleHandling": {"subscriptionLogic": "Parents receive updates for all children across all schools", "notificationRouting": "Notifications include child name and school context", "uiUpdates": "Updates appear in relevant child/school sections"}}}}, "permissionAggregation": {"description": "How permissions are combined for users with multiple roles", "algorithm": {"rules": ["If any role grants permission, access is allowed (OR logic)", "Most restrictive permission applies for conflicting rules", "School-specific permissions only apply to that school", "Permissions are aggregated at login and cached for performance", "Real-time permission updates when roles change"]}, "implementation": {"backend": "CASL combines permissions from all roles/schools", "frontend": "Zustand stores unified permission object", "ui": "Interface shows/hides features based on aggregated permissions"}}, "crossModuleWorkflows": {"teacherParentExample": {"description": "A user who is both a teacher at School A and a parent at School B", "involvedModules": ["Staff Module", "Students Module", "Communication Module", "Timetable Module"], "dataFlow": "Staff data from School A and student data from School B are combined in unified dashboard", "permissions": "Combined teacher permissions for School A and parent permissions for School B", "uiPresentation": "Dashboard has Teacher tab for School A and Parent tab for School B", "realTimeUpdates": "Receives teaching updates from School A and student updates from School B"}, "multiSchoolTeacherExample": {"description": "A teacher who teaches at multiple schools", "involvedModules": ["Staff Module", "Academy Module", "Timetable Module", "Attendance Module"], "dataFlow": "Teaching assignments from all schools are aggregated", "permissions": "Teacher permissions apply to all schools they teach at", "uiPresentation": "School selector dropdown to filter teaching assignments", "realTimeUpdates": "Receives updates from all schools they teach at"}}, "integrationPrinciples": {"dataFlow": "All modules use standardized GraphQL schemas for data exchange", "authentication": "Identity Module provides centralized auth for all modules", "realTimeUpdates": "Socket.IO enables real-time data synchronization across modules", "permissionModel": "CASL-based permissions aggregated from Identity Module", "errorHandling": "Standardized error reporting through Analytics Module"}, "systemObjectives": {"unifiedInterface": "All modules present data through role-aware unified interface", "crossSchoolOperations": "Modules support operations across multiple schools", "realTimeCapabilities": "All modules support real-time updates via Socket.IO", "permissionAggregation": "Identity Module aggregates permissions across all modules", "dataConsistency": "Centralized state management through Zustand ensures consistency"}}