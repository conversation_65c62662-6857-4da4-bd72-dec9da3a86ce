"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getResolvers = exports.getTypeDefs = void 0;
const user_repository_1 = require("../infra/user.repository");
const auth_service_1 = require("../application/auth.service");
const errors_1 = require("../../../shared/kernel/errors");
const types_1 = require("../domain/types");
const getTypeDefs = () => `
  enum UserRole {
    SuperAdmin
    FounderInvestor
    Principal
    SchoolAdmin
    Teacher
    Cleaner
    Driver
    StaffOther
    Student
    ParentGuardian
  }

  type User {
    id: ID!
    email: String!
    firstName: String
    lastName: String
    role: UserRole!
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
  }

  type AuthPayload {
    token: String!
    user: User!
  }

  input RegisterInput {
    email: String!
    password: String!
    firstName: String
    lastName: String
  }

  input LoginInput {
    email: String!
    password: String!
  }

  input UpdateUserInput {
    firstName: String
    lastName: String
    role: UserRole
    isActive: Boolean
  }

  extend type Query {
    me: User
    users(skip: Int, take: Int): [User!]!
    user(id: ID!): User
  }

  extend type Mutation {
    register(input: RegisterInput!): AuthPayload!
    login(input: LoginInput!): AuthPayload!
    refreshToken: String!
    updateUser(id: ID!, input: UpdateUserInput!): User!
    deleteUser(id: ID!): Boolean!
  }
`;
exports.getTypeDefs = getTypeDefs;
const getResolvers = (prisma) => {
    const userRepository = (0, user_repository_1.createUserRepository)(prisma);
    const authService = (0, auth_service_1.createAuthService)(userRepository);
    const requireAuth = (context) => {
        if (!context.user) {
            throw errors_1.AppError.unauthenticated();
        }
        return context.user;
    };
    const requireAdmin = (context) => {
        const user = requireAuth(context);
        if (user.role !== types_1.UserRole.SuperAdmin) {
            throw errors_1.AppError.unauthorized('Admin access required');
        }
        return user;
    };
    return {
        Query: {
            me: async (_, __, context) => {
                const user = requireAuth(context);
                return userRepository.findById(user.id);
            },
            users: async (_, args, context) => {
                requireAdmin(context);
                return userRepository.findMany(args.skip, args.take);
            },
            user: async (_, args, context) => {
                requireAdmin(context);
                const user = await userRepository.findById(args.id);
                if (!user) {
                    throw errors_1.AppError.notFound('User', args.id);
                }
                return user;
            },
        },
        Mutation: {
            register: async (_, args) => {
                return authService.register(args.input);
            },
            login: async (_, args) => {
                return authService.login(args.input);
            },
            refreshToken: async (_, __, context) => {
                requireAuth(context);
                if (!context.token) {
                    throw errors_1.AppError.unauthenticated('No token provided');
                }
                return authService.refreshToken(context.token);
            },
            updateUser: async (_, args, context) => {
                const currentUser = requireAuth(context);
                if (currentUser.id !== args.id && currentUser.role !== types_1.UserRole.SuperAdmin) {
                    throw errors_1.AppError.unauthorized('You can only update your own profile');
                }
                const user = await userRepository.findById(args.id);
                if (!user) {
                    throw errors_1.AppError.notFound('User', args.id);
                }
                return userRepository.update(args.id, args.input);
            },
            deleteUser: async (_, args, context) => {
                requireAdmin(context);
                const user = await userRepository.findById(args.id);
                if (!user) {
                    throw errors_1.AppError.notFound('User', args.id);
                }
                await userRepository.delete(args.id);
                return true;
            },
        },
    };
};
exports.getResolvers = getResolvers;
//# sourceMappingURL=index.js.map