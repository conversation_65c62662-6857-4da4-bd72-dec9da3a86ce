"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUserRepository = void 0;
const generated_1 = require("../../../../prisma/generated");
const toDomainUser = (user) => ({
    id: user.id,
    email: user.email,
    firstName: user.firstName || undefined,
    lastName: user.lastName || undefined,
    role: user.role,
    isActive: user.isActive,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
});
const createUserRepository = (prisma) => {
    return {
        async findById(id) {
            const user = await prisma.user.findUnique({
                where: { id },
            });
            return user ? toDomain<PERSON>ser(user) : null;
        },
        async findByEmail(email) {
            const user = await prisma.user.findUnique({
                where: { email },
            });
            return user ? toDomain<PERSON>ser(user) : null;
        },
        async findByEmailWithPassword(email) {
            const user = await prisma.user.findUnique({
                where: { email },
            });
            if (!user)
                return null;
            return {
                ...toDomain<PERSON>ser(user),
                password: user.password,
            };
        },
        async findMany(skip = 0, take = 50) {
            const users = await prisma.user.findMany({
                skip,
                take,
                orderBy: { createdAt: 'desc' },
            });
            return users.map(toDomainUser);
        },
        async create(input) {
            const user = await prisma.user.create({
                data: {
                    email: input.email,
                    password: input.password,
                    firstName: input.firstName,
                    lastName: input.lastName,
                    role: input.role || generated_1.UserRole.Student,
                },
            });
            return toDomainUser(user);
        },
        async update(id, input) {
            const user = await prisma.user.update({
                where: { id },
                data: {
                    ...input,
                    role: input.role,
                },
            });
            return toDomainUser(user);
        },
        async delete(id) {
            await prisma.user.delete({
                where: { id },
            });
        },
        async count() {
            return prisma.user.count();
        },
    };
};
exports.createUserRepository = createUserRepository;
//# sourceMappingURL=user.repository.js.map