export enum ErrorCode {
  // Authentication & Authorization
  UNAUTHENTICATED = 'UNAUTHENTICATED',
  UNAUTHORIZED = 'UNAUTHORIZED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  
  // Validation
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  REQUIRED_FIELD_MISSING = 'REQUIRED_FIELD_MISSING',
  
  // Business Logic
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS',
  OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED',
  BUSINESS_RULE_VIOLATION = 'BUSINESS_RULE_VIOLATION',
  
  // System
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
}

export class AppError extends Error {
  constructor(
    public readonly code: ErrorCode,
    message: string,
    public readonly statusCode: number = 500,
    public readonly details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'AppError';
    Error.captureStackTrace(this, AppError);
  }

  static unauthenticated(message = 'Authentication required'): AppError {
    return new AppError(ErrorCode.UNAUTHENTICATED, message, 401);
  }

  static unauthorized(message = 'Insufficient permissions'): AppError {
    return new AppError(ErrorCode.UNAUTHORIZED, message, 403);
  }

  static invalidCredentials(message = 'Invalid credentials'): AppError {
    return new AppError(ErrorCode.INVALID_CREDENTIALS, message, 401);
  }

  static notFound(resource: string, id?: string): AppError {
    const message = id ? `${resource} with id ${id} not found` : `${resource} not found`;
    return new AppError(ErrorCode.RESOURCE_NOT_FOUND, message, 404);
  }

  static alreadyExists(resource: string, field?: string): AppError {
    const message = field ? `${resource} with this ${field} already exists` : `${resource} already exists`;
    return new AppError(ErrorCode.RESOURCE_ALREADY_EXISTS, message, 409);
  }

  static validation(message: string, details?: Record<string, unknown>): AppError {
    return new AppError(ErrorCode.VALIDATION_ERROR, message, 400, details);
  }

  static businessRule(message: string): AppError {
    return new AppError(ErrorCode.BUSINESS_RULE_VIOLATION, message, 400);
  }

  static internal(message = 'Internal server error'): AppError {
    return new AppError(ErrorCode.INTERNAL_SERVER_ERROR, message, 500);
  }
}

export interface ErrorResponse {
  code: ErrorCode;
  message: string;
  statusCode: number;
  details?: Record<string, unknown>;
  timestamp: string;
  path?: string;
}

export function formatError(error: Error, path?: string): ErrorResponse {
  if (error instanceof AppError) {
    return {
      code: error.code,
      message: error.message,
      statusCode: error.statusCode,
      details: error.details,
      timestamp: new Date().toISOString(),
      path,
    };
  }

  // Handle Prisma errors
  if (error.name === 'PrismaClientKnownRequestError') {
    const prismaError = error as any;
    switch (prismaError.code) {
      case 'P2002':
        return formatError(AppError.alreadyExists('Resource', 'unique field'), path);
      case 'P2025':
        return formatError(AppError.notFound('Resource'), path);
      default:
        return formatError(AppError.internal('Database operation failed'), path);
    }
  }

  // Handle validation errors (Zod, etc.)
  if (error.name === 'ZodError') {
    return formatError(AppError.validation('Validation failed', { errors: (error as any).errors }), path);
  }

  // Default to internal server error
  const errorMessage = typeof error.message === 'string' && error.message.length > 0
    ? error.message
    : 'An unexpected error occurred';
  const internalError = AppError.internal(errorMessage);
  return {
    code: internalError.code,
    message: internalError.message,
    statusCode: internalError.statusCode,
    details: internalError.details,
    timestamp: new Date().toISOString(),
    path,
  };
}
