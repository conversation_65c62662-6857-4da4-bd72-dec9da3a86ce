-- CreateEnum
CREATE TYPE "public"."UserRole" AS ENUM ('<PERSON>Ad<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Principal', '<PERSON><PERSON><PERSON><PERSON>', 'Teacher', 'Cleaner', 'Driver', '<PERSON>O<PERSON>', 'Student', '<PERSON><PERSON><PERSON><PERSON>ian');

-- CreateTable
CREATE TABLE "public"."User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "role" "public"."UserRole" NOT NULL DEFAULT 'Student',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "public"."User"("email");
