export enum UserRole {
  SuperAdmin = 'SuperAdmin',
  FounderInvestor = 'FounderInvestor',
  Principal = 'Principal',
  SchoolAdmin = 'SchoolAdmin',
  Teacher = 'Teacher',
  Cleaner = 'Cleaner',
  Driver = 'Driver',
  StaffOther = 'StaffOther',
  Student = 'Student',
  ParentGuardian = 'ParentGuardian',
}

export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserInput {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  role?: UserRole;
}

export interface UpdateUserInput {
  firstName?: string;
  lastName?: string;
  role?: UserRole;
  isActive?: boolean;
}

export interface LoginInput {
  email: string;
  password: string;
}

export interface RegisterInput {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface AuthPayload {
  token: string;
  user: User;
}

export interface JWTPayload {
  sub: string;
  email: string;
  role: User<PERSON><PERSON>;
}
