import Fastify, { FastifyInstance } from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import jwt from '@fastify/jwt';
import rateLimit from '@fastify/rate-limit';
import mercurius from 'mercurius';
import { PrismaClient } from '../prisma/generated';
// import { logger } from './shared/kernel/logger';
import { AppError, formatError } from './shared/kernel/errors';
import { loadModules } from './shared/kernel/module-loader';

export interface AppContext {
  prisma: PrismaClient;
  user?: {
    id: string;
    email: string;
    role: string;
  };
  token?: string;
}

export async function createApp(): Promise<FastifyInstance> {
  const app = Fastify({
    logger: {
      level: process.env.LOG_LEVEL || 'info',
      transport: process.env.NODE_ENV === 'development' ? {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'HH:MM:ss Z',
          ignore: 'pid,hostname',
        },
      } : undefined,
    },
    trustProxy: true,
  });

  // Initialize Prisma
  const prisma = new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
  });

  // Register plugins
  await app.register(helmet, {
    contentSecurityPolicy: false, // Disable for GraphQL playground
  });

  await app.register(cors, {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:1420'],
    credentials: true,
  });

  await app.register(rateLimit, {
    max: parseInt(process.env.RATE_LIMIT_MAX || '100'),
    timeWindow: '1 minute',
  });

  await app.register(jwt, {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    sign: {
      expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    },
  });

  // Load all modules and get their GraphQL schemas
  const { typeDefs, resolvers } = await loadModules(prisma);

  // Register Mercurius GraphQL
  await app.register(mercurius as any, {
    schema: typeDefs,
    resolvers,
    context: async (request: any): Promise<AppContext> => {
      const context: AppContext = { prisma };

      // Extract JWT token if present
      try {
        const token = request.headers.authorization?.replace('Bearer ', '');
        if (token) {
          const decoded = app.jwt.verify(token) as any;
          context.user = {
            id: decoded.sub,
            email: decoded.email,
            role: decoded.role,
          };
          context.token = token;
        }
      } catch (error) {
        // Token is invalid, but we don't throw here - let resolvers handle auth
        app.log.debug('Invalid JWT token provided');
      }

      return context;
    },
    graphiql: process.env.NODE_ENV === 'development' ? 'playground' : false,
    errorFormatter: (execution: any, _context: any) => {
      const { errors } = execution;
      if (!errors) return execution;

      const formattedErrors = errors.map((error: any) => {
        const formatted = formatError(error.originalError || error);
        return {
          message: formatted.message,
          extensions: {
            code: formatted.code,
            statusCode: formatted.statusCode,
            details: formatted.details,
            timestamp: formatted.timestamp,
          },
          locations: error.locations,
          path: error.path,
        };
      });

      return {
        ...execution,
        errors: formattedErrors,
      };
    },
  });

  // Global error handler
  app.setErrorHandler(async (error, request, reply) => {
    const formatted = formatError(error, request.url);
    
    app.log.error({
      error: error.message,
      stack: error.stack,
      url: request.url,
      method: request.method,
    }, 'Unhandled error');

    await reply.status(formatted.statusCode).send({
      error: {
        code: formatted.code,
        message: formatted.message,
        details: formatted.details,
        timestamp: formatted.timestamp,
      },
    });
  });

  // Health check endpoint
  app.get('/health', async () => {
    try {
      await prisma.$queryRaw`SELECT 1`;
      return { status: 'ok', timestamp: new Date().toISOString() };
    } catch (error) {
      throw AppError.internal('Database connection failed');
    }
  });

  // Graceful shutdown
  app.addHook('onClose', async () => {
    await prisma.$disconnect();
  });

  return app;
}
