import bcrypt from 'bcrypt';
import * as jwt from 'jsonwebtoken';
import { UserRepository } from '../infra/user.repository';
import { LoginInput, RegisterInput, AuthPayload, JWTPayload, UserRole } from '../domain/types';
import { AppError } from '../../../shared/kernel/errors';

export interface AuthService {
  register(input: RegisterInput): Promise<AuthPayload>;
  login(input: LoginInput): Promise<AuthPayload>;
  verifyToken(token: string): Promise<JWTPayload>;
  refreshToken(token: string): Promise<string>;
}

export const createAuthService = (
  userRepository: UserRepository,
  jwtSecret: string = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production'
): AuthService => {
  const SALT_ROUNDS = 12;
  const TOKEN_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

  const hashPassword = async (password: string): Promise<string> => {
    return bcrypt.hash(password, SALT_ROUNDS);
  };

  const comparePassword = async (password: string, hash: string): Promise<boolean> => {
    return bcrypt.compare(password, hash);
  };

  const generateToken = (payload: JWTPayload): string => {
    return jwt.sign(payload as any, jwtSecret, { expiresIn: TOKEN_EXPIRES_IN } as any);
  };

  const validatePassword = (password: string): void => {
    if (password.length < 8) {
      throw AppError.validation('Password must be at least 8 characters long');
    }
    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
      throw AppError.validation('Password must contain at least one uppercase letter, one lowercase letter, and one number');
    }
  };

  const validateEmail = (email: string): void => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw AppError.validation('Invalid email format');
    }
  };

  return {
    async register(input: RegisterInput): Promise<AuthPayload> {
      validateEmail(input.email);
      validatePassword(input.password);

      // Check if user already exists
      const existingUser = await userRepository.findByEmail(input.email);
      if (existingUser) {
        throw AppError.alreadyExists('User', 'email');
      }

      // Hash password and create user
      const hashedPassword = await hashPassword(input.password);
      const user = await userRepository.create({
        email: input.email,
        password: hashedPassword,
        firstName: input.firstName,
        lastName: input.lastName,
        role: UserRole.Student,
      });

      // Generate JWT token
      const tokenPayload: JWTPayload = {
        sub: user.id,
        email: user.email,
        role: user.role,
      };
      const token = generateToken(tokenPayload);

      return {
        token,
        user,
      };
    },

    async login(input: LoginInput): Promise<AuthPayload> {
      validateEmail(input.email);

      const userWithPassword = await userRepository.findByEmailWithPassword(input.email);

      if (!userWithPassword || !userWithPassword.isActive) {
        throw AppError.invalidCredentials();
      }

      const isValidPassword = await comparePassword(input.password, userWithPassword.password);
      if (!isValidPassword) {
        throw AppError.invalidCredentials();
      }

      // Generate JWT token
      const tokenPayload: JWTPayload = {
        sub: userWithPassword.id,
        email: userWithPassword.email,
        role: userWithPassword.role,
      };
      const token = generateToken(tokenPayload);

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password, ...user } = userWithPassword;

      return {
        token,
        user,
      };
    },

    async verifyToken(token: string): Promise<JWTPayload> {
      try {
        const decoded = jwt.verify(token, jwtSecret) as JWTPayload;
        
        // Verify user still exists and is active
        const user = await userRepository.findById(decoded.sub);
        if (!user || !user.isActive) {
          throw AppError.unauthenticated('User no longer exists or is inactive');
        }

        return decoded;
      } catch (error) {
        if (error instanceof jwt.JsonWebTokenError) {
          throw AppError.unauthenticated('Invalid token');
        }
        if (error instanceof jwt.TokenExpiredError) {
          throw AppError.unauthenticated('Token expired');
        }
        throw error;
      }
    },

    async refreshToken(token: string): Promise<string> {
      const decoded = await this.verifyToken(token);
      
      // Generate new token with same payload
      const newTokenPayload: JWTPayload = {
        sub: decoded.sub,
        email: decoded.email,
        role: decoded.role,
      };
      
      return generateToken(newTokenPayload);
    },
  };
};
