"use client";
import {
  createSlot
} from "./chunk-RLETK72U.js";
import {
  require_jsx_runtime
} from "./chunk-CMYK4I76.js";
import {
  require_react_dom
} from "./chunk-THFEYB23.js";
import {
  require_react
} from "./chunk-V7IX5Q3P.js";
import {
  __toESM
} from "./chunk-5WRI5ZAA.js";

// node_modules/.pnpm/@radix-ui+react-label@2.1.7_d0e794566b9c0660549b54e794921e40/node_modules/@radix-ui/react-label/dist/index.mjs
var React2 = __toESM(require_react(), 1);

// node_modules/.pnpm/@radix-ui+react-primitive@2_091b81b7af523dbb2fc8a703d4690814/node_modules/@radix-ui/react-primitive/dist/index.mjs
var React = __toESM(require_react(), 1);
var ReactDOM = __toESM(require_react_dom(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var NODES = [
  "a",
  "button",
  "div",
  "form",
  "h2",
  "h3",
  "img",
  "input",
  "label",
  "li",
  "nav",
  "ol",
  "p",
  "select",
  "span",
  "svg",
  "ul"
];
var Primitive = NODES.reduce((primitive, node) => {
  const Slot = createSlot(`Primitive.${node}`);
  const Node = React.forwardRef((props, forwardedRef) => {
    const { asChild, ...primitiveProps } = props;
    const Comp = asChild ? Slot : node;
    if (typeof window !== "undefined") {
      window[Symbol.for("radix-ui")] = true;
    }
    return (0, import_jsx_runtime.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });
  });
  Node.displayName = `Primitive.${node}`;
  return { ...primitive, [node]: Node };
}, {});

// node_modules/.pnpm/@radix-ui+react-label@2.1.7_d0e794566b9c0660549b54e794921e40/node_modules/@radix-ui/react-label/dist/index.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var NAME = "Label";
var Label = React2.forwardRef((props, forwardedRef) => {
  return (0, import_jsx_runtime2.jsx)(
    Primitive.label,
    {
      ...props,
      ref: forwardedRef,
      onMouseDown: (event) => {
        const target = event.target;
        if (target.closest("button, input, select, textarea")) return;
        props.onMouseDown?.(event);
        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();
      }
    }
  );
});
Label.displayName = NAME;
var Root = Label;
export {
  Label,
  Root
};
//# sourceMappingURL=@radix-ui_react-label.js.map
