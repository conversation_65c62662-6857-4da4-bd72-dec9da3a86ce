import { AuthPayload, LoginInput, RegisterInput } from '../types';

const GQL_URL = 'http://localhost:4000/graphql';

async function graphqlRequest(query: string, variables: Record<string, any>): Promise<any> {
  const response = await fetch(GQL_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ query, variables }),
  });

  if (!response.ok) {
    throw new Error('Network response was not ok');
  }

  const json = await response.json();
  if (json.errors) {
    throw new Error(json.errors.map((e: any) => e.message).join('\n'));
  }

  return json.data;
}

export const authService = {
  async login(input: LoginInput): Promise<AuthPayload> {
    const query = `
      mutation Login($input: LoginInput!) {
        login(input: $input) {
          token
          user {
            id
            email
            firstName
            lastName
            role
            isActive
          }
        }
      }
    `;
    const data = await graphqlRequest(query, { input });
    return data.login;
  },

  async register(input: RegisterInput): Promise<AuthPayload> {
    const query = `
      mutation Register($input: RegisterInput!) {
        register(input: $input) {
          token
          user {
            id
            email
            firstName
            lastName
            role
            isActive
          }
        }
      }
    `;
    const data = await graphqlRequest(query, { input });
    return data.register;
  },
};
