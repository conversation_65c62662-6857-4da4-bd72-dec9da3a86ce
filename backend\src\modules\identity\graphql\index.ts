import { PrismaClient } from '../../../../prisma/generated';
import { createUserRepository } from '../infra/user.repository';
import { createAuthService } from '../application/auth.service';
import { AppError } from '../../../shared/kernel/errors';
import { AppContext } from '../../../app';
import { UserRole } from '../domain/types';

export const getTypeDefs = (): string => `
  enum UserRole {
    SuperAdmin
    FounderInvestor
    Principal
    SchoolAdmin
    Teacher
    Cleaner
    Driver
    StaffOther
    Student
    ParentGuardian
  }

  type User {
    id: ID!
    email: String!
    firstName: String
    lastName: String
    role: UserRole!
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
  }

  type AuthPayload {
    token: String!
    user: User!
  }

  input RegisterInput {
    email: String!
    password: String!
    firstName: String
    lastName: String
  }

  input LoginInput {
    email: String!
    password: String!
  }

  input UpdateUserInput {
    firstName: String
    lastName: String
    role: UserRole
    isActive: Boolean
  }

  extend type Query {
    me: User
    users(skip: Int, take: Int): [User!]!
    user(id: ID!): User
  }

  extend type Mutation {
    register(input: RegisterInput!): AuthPayload!
    login(input: LoginInput!): AuthPayload!
    refreshToken: String!
    updateUser(id: ID!, input: UpdateUserInput!): User!
    deleteUser(id: ID!): Boolean!
  }
`;

export const getResolvers = (prisma: PrismaClient) => {
  const userRepository = createUserRepository(prisma);
  const authService = createAuthService(userRepository);

  const requireAuth = (context: AppContext) => {
    if (!context.user) {
      throw AppError.unauthenticated();
    }
    return context.user;
  };

  const requireAdmin = (context: AppContext) => {
    const user = requireAuth(context);
    if (user.role !== UserRole.SuperAdmin) {
      throw AppError.unauthorized('Admin access required');
    }
    return user;
  };

  return {
    Query: {
      me: async (_: any, __: any, context: AppContext) => {
        const user = requireAuth(context);
        return userRepository.findById(user.id);
      },

      users: async (_: any, args: { skip?: number; take?: number }, context: AppContext) => {
        requireAdmin(context);
        return userRepository.findMany(args.skip, args.take);
      },

      user: async (_: any, args: { id: string }, context: AppContext) => {
        requireAdmin(context);
        const user = await userRepository.findById(args.id);
        if (!user) {
          throw AppError.notFound('User', args.id);
        }
        return user;
      },
    },

    Mutation: {
      register: async (_: any, args: { input: any }) => {
        return authService.register(args.input);
      },

      login: async (_: any, args: { input: any }) => {
        return authService.login(args.input);
      },

      refreshToken: async (_: any, __: any, context: AppContext) => {
        requireAuth(context);
        if (!context.token) {
          throw AppError.unauthenticated('No token provided');
        }
        return authService.refreshToken(context.token);
      },

      updateUser: async (_: any, args: { id: string; input: any }, context: AppContext) => {
        const currentUser = requireAuth(context);
        
        if (currentUser.id !== args.id && currentUser.role !== UserRole.SuperAdmin) {
          throw AppError.unauthorized('You can only update your own profile');
        }

        const user = await userRepository.findById(args.id);
        if (!user) {
          throw AppError.notFound('User', args.id);
        }

        return userRepository.update(args.id, args.input);
      },

      deleteUser: async (_: any, args: { id: string }, context: AppContext) => {
        requireAdmin(context);
        
        const user = await userRepository.findById(args.id);
        if (!user) {
          throw AppError.notFound('User', args.id);
        }

        await userRepository.delete(args.id);
        return true;
      },
    },
  };
};
