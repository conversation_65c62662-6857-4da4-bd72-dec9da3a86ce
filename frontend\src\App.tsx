import { useState } from "react";
import "./App.css";
import { LoginForm } from "./modules/identity/components/LoginForm";
import { RegisterForm } from "./modules/identity/components/RegisterForm";
import { But<PERSON> } from "./components/ui/button";
import { useAuthStore } from "./modules/identity/store/auth.store";

function App() {
  const [showLogin, setShowLogin] = useState(true);
  const { user, clearAuth } = useAuthStore();

  if (user) {
    return (
      <div className="w-screen h-screen flex flex-col justify-center items-center bg-background">
        <h1 className="text-2xl font-bold mb-4">Welcome, {user.firstName || user.email}!</h1>
        <Button onClick={clearAuth}>Logout</Button>
      </div>
    );
  }

  return (
    <div className="w-screen h-screen flex flex-col justify-center items-center bg-background">
      <div className="p-4 absolute top-0 right-0">
        <Button onClick={() => setShowLogin(!showLogin)}>
          {showLogin ? "Switch to Register" : "Switch to Login"}
        </Button>
      </div>
      {showLogin ? <LoginForm /> : <RegisterForm />}
    </div>
  );
}

export default App;