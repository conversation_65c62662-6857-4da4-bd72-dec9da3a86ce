{"version": 3, "sources": ["../../.pnpm/zustand@5.0.8_@types+react@19.1.12_react@19.1.1/node_modules/zustand/esm/vanilla.mjs", "../../.pnpm/zustand@5.0.8_@types+react@19.1.12_react@19.1.1/node_modules/zustand/esm/react.mjs"], "sourcesContent": ["const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = ((createState) => createState ? createStoreImpl(createState) : createStoreImpl);\n\nexport { createStore };\n", "import React from 'react';\nimport { createStore } from 'zustand/vanilla';\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = React.useSyncExternalStore(\n    api.subscribe,\n    React.useCallback(() => selector(api.getState()), [api, selector]),\n    React.useCallback(() => selector(api.getInitialState()), [api, selector])\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = createStore(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = ((createState) => createState ? createImpl(createState) : createImpl);\n\nexport { create, useStore };\n"], "mappings": ";;;;;;;;AAAA,IAAM,kBAAkB,CAAC,gBAAgB;AACvC,MAAI;AACJ,QAAM,YAA4B,oBAAI,IAAI;AAC1C,QAAM,WAAW,CAAC,SAAS,YAAY;AACrC,UAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACnE,QAAI,CAAC,OAAO,GAAG,WAAW,KAAK,GAAG;AAChC,YAAM,gBAAgB;AACtB,eAAS,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,QAAQ,YAAY,OAAO,OAAO,CAAC,GAAG,OAAO,SAAS;AAC1I,gBAAU,QAAQ,CAAC,aAAa,SAAS,OAAO,aAAa,CAAC;AAAA,IAChE;AAAA,EACF;AACA,QAAM,WAAW,MAAM;AACvB,QAAM,kBAAkB,MAAM;AAC9B,QAAM,YAAY,CAAC,aAAa;AAC9B,cAAU,IAAI,QAAQ;AACtB,WAAO,MAAM,UAAU,OAAO,QAAQ;AAAA,EACxC;AACA,QAAM,MAAM,EAAE,UAAU,UAAU,iBAAiB,UAAU;AAC7D,QAAM,eAAe,QAAQ,YAAY,UAAU,UAAU,GAAG;AAChE,SAAO;AACT;AACA,IAAM,eAAe,CAAC,gBAAgB,cAAc,gBAAgB,WAAW,IAAI;;;ACrBnF,mBAAkB;AAGlB,IAAM,WAAW,CAAC,QAAQ;AAC1B,SAAS,SAAS,KAAK,WAAW,UAAU;AAC1C,QAAM,QAAQ,aAAAA,QAAM;AAAA,IAClB,IAAI;AAAA,IACJ,aAAAA,QAAM,YAAY,MAAM,SAAS,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC;AAAA,IACjE,aAAAA,QAAM,YAAY,MAAM,SAAS,IAAI,gBAAgB,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC;AAAA,EAC1E;AACA,eAAAA,QAAM,cAAc,KAAK;AACzB,SAAO;AACT;AACA,IAAM,aAAa,CAAC,gBAAgB;AAClC,QAAM,MAAM,YAAY,WAAW;AACnC,QAAM,gBAAgB,CAAC,aAAa,SAAS,KAAK,QAAQ;AAC1D,SAAO,OAAO,eAAe,GAAG;AAChC,SAAO;AACT;AACA,IAAM,UAAU,CAAC,gBAAgB,cAAc,WAAW,WAAW,IAAI;", "names": ["React"]}