{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../../../../src/shared/kernel/errors.ts"], "names": [], "mappings": "AAAA,oBAAY,SAAS;IAEnB,eAAe,oBAAoB;IACnC,YAAY,iBAAiB;IAC7B,mBAAmB,wBAAwB;IAC3C,aAAa,kBAAkB;IAG/B,gBAAgB,qBAAqB;IACrC,aAAa,kBAAkB;IAC/B,sBAAsB,2BAA2B;IAGjD,kBAAkB,uBAAuB;IACzC,uBAAuB,4BAA4B;IACnD,qBAAqB,0BAA0B;IAC/C,uBAAuB,4BAA4B;IAGnD,qBAAqB,0BAA0B;IAC/C,cAAc,mBAAmB;IACjC,sBAAsB,2BAA2B;IACjD,mBAAmB,wBAAwB;CAC5C;AAED,qBAAa,QAAS,SAAQ,KAAK;aAEf,IAAI,EAAE,SAAS;aAEf,UAAU,EAAE,MAAM;aAClB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;gBAHjC,IAAI,EAAE,SAAS,EAC/B,OAAO,EAAE,MAAM,EACC,UAAU,GAAE,MAAY,EACxB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,YAAA;IAOnD,MAAM,CAAC,eAAe,CAAC,OAAO,SAA4B,GAAG,QAAQ;IAIrE,MAAM,CAAC,YAAY,CAAC,OAAO,SAA6B,GAAG,QAAQ;IAInE,MAAM,CAAC,kBAAkB,CAAC,OAAO,SAAwB,GAAG,QAAQ;IAIpE,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,MAAM,GAAG,QAAQ;IAKxD,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,QAAQ;IAKhE,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,QAAQ;IAI/E,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,QAAQ;IAI9C,MAAM,CAAC,QAAQ,CAAC,OAAO,SAA0B,GAAG,QAAQ;CAG7D;AAED,MAAM,WAAW,aAAa;IAC5B,IAAI,EAAE,SAAS,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC;IAChB,UAAU,EAAE,MAAM,CAAC;IACnB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAClC,SAAS,EAAE,MAAM,CAAC;IAClB,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,wBAAgB,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,aAAa,CA2CtE"}