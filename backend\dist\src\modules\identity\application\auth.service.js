"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAuthService = void 0;
const bcrypt_1 = __importDefault(require("bcrypt"));
const jwt = __importStar(require("jsonwebtoken"));
const types_1 = require("../domain/types");
const errors_1 = require("../../../shared/kernel/errors");
const createAuthService = (userRepository, jwtSecret = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production') => {
    const SALT_ROUNDS = 12;
    const TOKEN_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
    const hashPassword = async (password) => {
        return bcrypt_1.default.hash(password, SALT_ROUNDS);
    };
    const comparePassword = async (password, hash) => {
        return bcrypt_1.default.compare(password, hash);
    };
    const generateToken = (payload) => {
        return jwt.sign(payload, jwtSecret, { expiresIn: TOKEN_EXPIRES_IN });
    };
    const validatePassword = (password) => {
        if (password.length < 8) {
            throw errors_1.AppError.validation('Password must be at least 8 characters long');
        }
        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
            throw errors_1.AppError.validation('Password must contain at least one uppercase letter, one lowercase letter, and one number');
        }
    };
    const validateEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            throw errors_1.AppError.validation('Invalid email format');
        }
    };
    return {
        async register(input) {
            validateEmail(input.email);
            validatePassword(input.password);
            const existingUser = await userRepository.findByEmail(input.email);
            if (existingUser) {
                throw errors_1.AppError.alreadyExists('User', 'email');
            }
            const hashedPassword = await hashPassword(input.password);
            const user = await userRepository.create({
                email: input.email,
                password: hashedPassword,
                firstName: input.firstName,
                lastName: input.lastName,
                role: types_1.UserRole.Student,
            });
            const tokenPayload = {
                sub: user.id,
                email: user.email,
                role: user.role,
            };
            const token = generateToken(tokenPayload);
            return {
                token,
                user,
            };
        },
        async login(input) {
            validateEmail(input.email);
            const userWithPassword = await userRepository.findByEmailWithPassword(input.email);
            if (!userWithPassword || !userWithPassword.isActive) {
                throw errors_1.AppError.invalidCredentials();
            }
            const isValidPassword = await comparePassword(input.password, userWithPassword.password);
            if (!isValidPassword) {
                throw errors_1.AppError.invalidCredentials();
            }
            const tokenPayload = {
                sub: userWithPassword.id,
                email: userWithPassword.email,
                role: userWithPassword.role,
            };
            const token = generateToken(tokenPayload);
            const { password, ...user } = userWithPassword;
            return {
                token,
                user,
            };
        },
        async verifyToken(token) {
            try {
                const decoded = jwt.verify(token, jwtSecret);
                const user = await userRepository.findById(decoded.sub);
                if (!user || !user.isActive) {
                    throw errors_1.AppError.unauthenticated('User no longer exists or is inactive');
                }
                return decoded;
            }
            catch (error) {
                if (error instanceof jwt.JsonWebTokenError) {
                    throw errors_1.AppError.unauthenticated('Invalid token');
                }
                if (error instanceof jwt.TokenExpiredError) {
                    throw errors_1.AppError.unauthenticated('Token expired');
                }
                throw error;
            }
        },
        async refreshToken(token) {
            const decoded = await this.verifyToken(token);
            const newTokenPayload = {
                sub: decoded.sub,
                email: decoded.email,
                role: decoded.role,
            };
            return generateToken(newTokenPayload);
        },
    };
};
exports.createAuthService = createAuthService;
//# sourceMappingURL=auth.service.js.map