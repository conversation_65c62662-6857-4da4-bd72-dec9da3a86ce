import { PrismaClient, UserRole as PrismaUserRole } from '../../../../prisma/generated';
import { CreateUserInput, UpdateUserInput, User, UserRole } from '../domain/types';

export interface UserRepository {
  findById(id: string): Promise<User | null>;
  findByEmail(email: string): Promise<User | null>;
  findByEmailWithPassword(email: string): Promise<(User & { password: string }) | null>;
  findMany(skip?: number, take?: number): Promise<User[]>;
  create(input: CreateUserInput & { password: string }): Promise<User>;
  update(id: string, input: UpdateUserInput): Promise<User>;
  delete(id: string): Promise<void>;
  count(): Promise<number>;
}

// Helper to map Prisma User to Domain User
const toDomainUser = (user: any): User => ({
  id: user.id,
  email: user.email,
  firstName: user.firstName || undefined,
  lastName: user.lastName || undefined,
  role: user.role as User<PERSON>ole,
  isActive: user.isActive,
  createdAt: user.createdAt,
  updatedAt: user.updatedAt,
});

export const createUserRepository = (prisma: PrismaClient): UserRepository => {
  return {
    async findById(id: string): Promise<User | null> {
      const user = await prisma.user.findUnique({
        where: { id },
      });
      return user ? toDomainUser(user) : null;
    },

    async findByEmail(email: string): Promise<User | null> {
      const user = await prisma.user.findUnique({
        where: { email },
      });
      return user ? toDomainUser(user) : null;
    },

    async findByEmailWithPassword(email: string): Promise<(User & { password: string }) | null> {
      const user = await prisma.user.findUnique({
        where: { email },
      });

      if (!user) return null;

      return {
        ...toDomainUser(user),
        password: user.password,
      };
    },

    async findMany(skip = 0, take = 50): Promise<User[]> {
      const users = await prisma.user.findMany({
        skip,
        take,
        orderBy: { createdAt: 'desc' },
      });
      return users.map(toDomainUser);
    },

    async create(input: CreateUserInput & { password: string }): Promise<User> {
      const user = await prisma.user.create({
        data: {
          email: input.email,
          password: input.password,
          firstName: input.firstName,
          lastName: input.lastName,
          role: (input.role as PrismaUserRole) || PrismaUserRole.Student,
        },
      });
      return toDomainUser(user);
    },

    async update(id: string, input: UpdateUserInput): Promise<User> {
      const user = await prisma.user.update({
        where: { id },
        data: {
          ...input,
          role: input.role as PrismaUserRole,
        },
      });
      return toDomainUser(user);
    },

    async delete(id: string): Promise<void> {
      await prisma.user.delete({
        where: { id },
      });
    },

    async count(): Promise<number> {
      return prisma.user.count();
    },
  };
};
