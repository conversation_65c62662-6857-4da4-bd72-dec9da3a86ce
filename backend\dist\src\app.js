"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApp = createApp;
const fastify_1 = __importDefault(require("fastify"));
const cors_1 = __importDefault(require("@fastify/cors"));
const helmet_1 = __importDefault(require("@fastify/helmet"));
const jwt_1 = __importDefault(require("@fastify/jwt"));
const rate_limit_1 = __importDefault(require("@fastify/rate-limit"));
const mercurius_1 = __importDefault(require("mercurius"));
const generated_1 = require("../prisma/generated");
const errors_1 = require("./shared/kernel/errors");
const module_loader_1 = require("./shared/kernel/module-loader");
async function createApp() {
    const app = (0, fastify_1.default)({
        logger: {
            level: process.env.LOG_LEVEL || 'info',
            transport: process.env.NODE_ENV === 'development' ? {
                target: 'pino-pretty',
                options: {
                    colorize: true,
                    translateTime: 'HH:MM:ss Z',
                    ignore: 'pid,hostname',
                },
            } : undefined,
        },
        trustProxy: true,
    });
    const prisma = new generated_1.PrismaClient({
        log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
    });
    await app.register(helmet_1.default, {
        contentSecurityPolicy: false,
    });
    await app.register(cors_1.default, {
        origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:1420'],
        credentials: true,
    });
    await app.register(rate_limit_1.default, {
        max: parseInt(process.env.RATE_LIMIT_MAX || '100'),
        timeWindow: '1 minute',
    });
    await app.register(jwt_1.default, {
        secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
        sign: {
            expiresIn: process.env.JWT_EXPIRES_IN || '7d',
        },
    });
    const { typeDefs, resolvers } = await (0, module_loader_1.loadModules)(prisma);
    await app.register(mercurius_1.default, {
        schema: typeDefs,
        resolvers,
        context: async (request) => {
            const context = { prisma };
            try {
                const token = request.headers.authorization?.replace('Bearer ', '');
                if (token) {
                    const decoded = app.jwt.verify(token);
                    context.user = {
                        id: decoded.sub,
                        email: decoded.email,
                        role: decoded.role,
                    };
                    context.token = token;
                }
            }
            catch (error) {
                app.log.debug('Invalid JWT token provided');
            }
            return context;
        },
        graphiql: process.env.NODE_ENV === 'development' ? 'playground' : false,
        errorFormatter: (execution, _context) => {
            const { errors } = execution;
            if (!errors)
                return execution;
            const formattedErrors = errors.map((error) => {
                const formatted = (0, errors_1.formatError)(error.originalError || error);
                return {
                    message: formatted.message,
                    extensions: {
                        code: formatted.code,
                        statusCode: formatted.statusCode,
                        details: formatted.details,
                        timestamp: formatted.timestamp,
                    },
                    locations: error.locations,
                    path: error.path,
                };
            });
            return {
                ...execution,
                errors: formattedErrors,
            };
        },
    });
    app.setErrorHandler(async (error, request, reply) => {
        const formatted = (0, errors_1.formatError)(error, request.url);
        app.log.error({
            error: error.message,
            stack: error.stack,
            url: request.url,
            method: request.method,
        }, 'Unhandled error');
        await reply.status(formatted.statusCode).send({
            error: {
                code: formatted.code,
                message: formatted.message,
                details: formatted.details,
                timestamp: formatted.timestamp,
            },
        });
    });
    app.get('/health', async () => {
        try {
            await prisma.$queryRaw `SELECT 1`;
            return { status: 'ok', timestamp: new Date().toISOString() };
        }
        catch (error) {
            throw errors_1.AppError.internal('Database connection failed');
        }
    });
    app.addHook('onClose', async () => {
        await prisma.$disconnect();
    });
    return app;
}
//# sourceMappingURL=app.js.map