[17:05:22 UTC] ERROR: Called reply with an invalid status code: undefined
    reqId: "req-2"
    req: {
      "method": "POST",
      "url": "/graphql",
      "host": "localhost:4000",
      "remoteAddress": "127.0.0.1",
      "remotePort": 59570
    }
    res: {
      "statusCode": 500
    }
    err: {
      "type": "FastifyError",
      "message": "Called reply with an invalid status code: undefined",
      "stack":
          FastifyError: Called reply with an invalid status code: undefined
              at Reply.code (C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm\fastify@5.5.0\node_modules\fastify\lib\reply.js:314:11)
              at Object.<anonymous> (C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm\mercurius@15.1.0_graphql@16.11.0\node_modules\mercurius\lib\routes.js:186:20)
              at handleError (C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm\fastify@5.5.0\node_modules\fastify\lib\error-handler.js:69:20)
              at onErrorHook (C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm\fastify@5.5.0\node_modules\fastify\lib\reply.js:815:5)
              at Reply.send (C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm\fastify@5.5.0\node_modules\fastify\lib\reply.js:140:5)
              at C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm\fastify@5.5.0\node_modules\fastify\lib\wrapThenable.js:67:13
              at process.processTicksAndRejections (node:internal/process/task_queues:105:5)    
      "code": "FST_ERR_BAD_STATUS_CODE",
      "name": "FastifyError",
      "statusCode": 500
    }
[17:05:22 UTC] INFO: request completed
    reqId: "req-2"
    res: {
      "statusCode": 500
    }
    responseTime: 25.9995000064373
