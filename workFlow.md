# Module Implementation Workflow

## I. Backend Module Implementation

### 1. Setup Module Structure
```
backend/src/modules/<module-name>/
├── application/  # Business logic
├── domain/       # Types/interfaces
├── graphql/      # Schema/resolvers
├── infra/        # Repositories
└── prisma/       # Schema file
```

### 2. Create Database Schema
- Create `prisma/schema.prisma` with models
- Add `@@schema("<module-name>")` to all models
- Define cross-module relations directly
- **No generator/datasource blocks**

### 3. Implement Core Components
- **Domain**: Create `domain/types.ts` with interfaces
- **Repository**: Create `infra/<module>.repository.ts` using `PrismaClient` from `@generated/client`
- **Service**: Create `application/<module>.service.ts` with business logic
- **GraphQL**: Create `graphql/index.ts` with type definitions and resolvers

### 4. Run Prisma Commands
```bash
cd backend
pnpm prisma:merge    # Combine schemas
pnpm prisma:generate # Generate client
pnpm prisma:migrate dev --name <migration-name>
```

## II. Frontend Module Implementation

### 1. Setup Module Structure
```
frontend/src/modules/<module-name>/
├── components/  # React components
├── hooks/       # Custom hooks
├── services/    # API calls
├── store/       # Zustand store
└── types.ts     # TypeScript types
```

### 2. Implement Core Components
- **Types**: Create `types.ts` with Zod schemas
- **Store**: Create Zustand store with persistence if needed
- **Service**: Create `services/<module>.service.ts` with GraphQL operations
- **Hooks**: Create custom hooks for data fetching/mutations
- **Components**: Build React components (use lazy loading)

### 3. Update Router
```tsx
// In router.tsx
const ModuleRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '<module>',
  component: lazy(() => import('../modules/<module>/components/Page'))
});
```

## III. Integration Essentials

### 1. Error Handling
- **Backend**: Use `AppError` from `shared/kernel/errors.ts`
- **Frontend**: Implement `try/catch` in services and components
- **GraphQL**: Use Apollo's `onError` link for global error handling

### 2. Real-Time Features
- **Backend**: Implement Socket.IO events in resolvers
- **Frontend**: Connect Socket.IO in `apollo.ts` and handle events

### 3. Permission System
- **Backend**: Implement CASL permissions in resolvers
- **Frontend**: Use `usePermissions()` hook for UI checks
- **Aggregation**: Combine permissions from all roles in `Identity` module

### 4. Cross-Module Operations
- **Backend**: Use `prisma.$transaction()` for atomic operations
- **Frontend**: Create unified services that call multiple modules
- **State**: Aggregate data in Zustand stores

## IV. Critical Implementation Patterns

### 1. Minimal GraphQL Query
```graphql
# backend/modules/<module>/graphql/index.ts
extend type Query {
  items: [Item!]!
}

extend type Mutation {
  createItem(input: CreateItemInput!): Item!
}
```

### 2. Minimal React Hook
```tsx
// frontend/src/modules/<module>/hooks/useItems.ts
export const useItems = () => {
  const { data } = useQuery<{ items: Item[] }>(GET_ITEMS);
  return { items: data?.items || [] };
};
```

### 3. Minimal Component
```tsx
// frontend/src/modules/<module>/components/ItemList.tsx
const ItemList = () => {
  const { items } = useItems();
  return (
    <ul>
      {items.map(item => <li key={item.id}>{item.name}</li>)}
    </ul>
  );
};
```

## V. Validation Checklist

Before completing a module:
- [ ] All models have `@@schema` attribute
- [ ] Cross-module relations work in transactions
- [ ] Permissions are properly aggregated
- [ ] Real-time updates work across modules
- [ ] Error handling covers all operations
- [ ] Components handle loading/error states
- [ ] Router uses lazy loading
- [ ] Module exports are properly registered
